import Image from 'next/image'
import Link from 'next/link'
import { ChevronRight } from 'lucide-react'
import { IArticle } from '@/types'
import { useTranslations } from 'next-intl'
import ShareIcons from './ShareIcons'

const Article = ({ date, title, image, id }: IArticle) => {
  const t = useTranslations()

  return (
    <div className="bg-white rounded-[24px] overflow-hidden basic_card_shadow">
      <div className="relative w-full h-[250px]">
        <Link href={`/article/${id}`}>
          <div className="relative w-full h-full">
            <Image
              src={image || '/images/img.png'}
              alt="Article Image"
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </div>
        </Link>
      </div>
      <div className="p-8">
        <div className="flex justify-between items-center flex-wrap">
          <p className="text-xl font-bold text-gray-01">{date}</p>
          <ShareIcons url={`/article/${id}`} />
        </div>
        <h3 className="text-primary-03 text-[28px] py-6">{title}</h3>
        <Link href="#" className="text-primary-02 font-bold text-2xl flex items-center">
          <ChevronRight />
          {t('articles.read_more')}
        </Link>
      </div>
    </div>
  )
}

export default Article
